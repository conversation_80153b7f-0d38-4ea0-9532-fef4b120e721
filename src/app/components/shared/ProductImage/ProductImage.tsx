import { cx } from "class-variance-authority"
import Image from "next/image"
import { useState } from "react"

import { IconLogoKickAvenuePrimary } from "@kickavenue/ui/components/icons"
import styles from "./ProductImage.module.css"
import { ProductImageProps } from "./ProductImage.type"

const ProductImage = ({ containerProps, imageProps }: ProductImageProps) => {
  const { className, ...rest } = containerProps || {}
  const { className: imageClassName, alt, ...restImageProps } = imageProps

  const [isError, setIsError] = useState(false)

  return (
    <div className={cx(styles["image-container"], className)} {...rest}>
      {!isError ? (
        <Image
          alt={alt}
          className={cx(styles.image, imageClassName)}
          onError={() => setIsError(true)}
          {...restImageProps}
        />
      ) : (
        <div className="flex h-full w-full items-center justify-center">
          <IconLogoKickAvenuePrimary className={cx("")} />
        </div>
      )}
    </div>
  )
}

export default ProductImage
