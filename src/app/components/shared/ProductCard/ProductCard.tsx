import {
  IconWishlistBold,
  IconWishlistOutline,
  Text,
} from "@kickavenue/ui/dist/src/components"
import { cx } from "class-variance-authority"

import ProductImage from "@shared/ProductImage"

import ClickableDiv from "../ClickableDiv"

import styles from "./ProductCard.module.css"
import { ProductCardProps } from "./ProductCard.type"
import ProductCardPrice from "./ProductCardPrice"

const ProductCard = ({
  imageProps,
  imageContainerProps,
  isWishlistActive,
  brandName,
  itemName,
  price,
  strikeThroughPrice,
  cardContainer,
  onWishlistClick,
  badge,
  itemSize,
}: ProductCardProps) => {
  const wishlistIconSize = 20
  const renderWishlistIcon = isWishlistActive ? (
    <IconWishlistBold width={wishlistIconSize} height={wishlistIconSize} />
  ) : (
    <IconWishlistOutline width={wishlistIconSize} height={wishlistIconSize} />
  )
  const { className: cardContainerClass, ...restCardContainer } =
    cardContainer || {}

  return (
    <div
      className={cx(styles["card-container"], cardContainerClass)}
      {...restCardContainer}
    >
      <ProductImage
        imageProps={imageProps}
        containerProps={{
          className: styles["product-image"],
          ...imageContainerProps,
        }}
      />
      <div className={styles["product-header"]}>
        {badge}
        <Text size="base" type="regular" state="secondary">
          {itemSize}
        </Text>
      </div>
      <button
        type="button"
        onClick={onWishlistClick}
        className={styles["wishlist-icon"]}
      >
        {renderWishlistIcon}
      </button>
      <ClickableDiv onClick={imageProps?.onClick} keyDownHandler={() => {}}>
        <Text
          size="sm"
          type="bold"
          state="primary"
          className={styles["brand-name"]}
        >
          {brandName}
        </Text>
      </ClickableDiv>
      <ClickableDiv onClick={imageProps?.onClick} keyDownHandler={() => {}}>
        <Text
          size="base"
          type="medium"
          state="primary"
          className={cx(styles["item-name"], "!line-clamp-2")}
        >
          {itemName}
        </Text>
      </ClickableDiv>
      <ClickableDiv onClick={imageProps?.onClick} keyDownHandler={() => {}}>
        <ProductCardPrice
          price={price}
          strikeThroughPrice={strikeThroughPrice}
        />
      </ClickableDiv>
    </div>
  )
}

export default ProductCard
