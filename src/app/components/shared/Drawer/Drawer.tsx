import { cx } from "class-variance-authority"
import { HTMLAttributes, ReactNode } from "react"

import useBodyOverflow from "@app/hooks/useBodyOverflow"
import ClickableDiv from "@shared/ClickableDiv"

export interface DrawerProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode
  placement: "left" | "right"
  open: boolean
  onClose: () => void
}

const Drawer = ({
  children,
  placement,
  open,
  onClose,
  className,
  ...rest
}: DrawerProps) => {
  useBodyOverflow(open)

  return (
    <div
      className={cx(
        "absolute top-0 size-full transition-all duration-300 ease-in-out",
        placement === "left" && "left-0",
        placement === "right" && "right-0",
        open && "translate-0",
        !open && placement === "left" && "-translate-x-full",
        !open && placement === "right" && "translate-x-full",
        className,
      )}
      {...rest}
    >
      {/* overlay */}
      <ClickableDiv
        keyDownHandler={onClose}
        onClick={onClose}
        className={cx(
          "absolute top-0 size-full bg-black-dim-40 opacity-0 transition-all duration-500 ease-in-out",
          placement === "left" && "left-0",
          placement === "right" && "right-0",
          open && "opacity-[0.6]",
        )}
      />
      {/* drawer content */}
      <div
        className={cx(
          "absolute top-0 z-50 h-full min-w-[262px] overflow-y-auto bg-white",
          placement === "left" && "left-0",
          placement === "right" && "right-0",
        )}
      >
        {children}
      </div>
    </div>
  )
}

export default Drawer
